"""Data structures for QBF formulas."""

from typing import List, Dict, Set, Sequence, FrozenSet
from dataclasses import dataclass, field
import itertools
import parse

VariableIndex = int
ClauseIndex = int

QuantifierType = parse.QuantifierType
    


@dataclass(frozen=True)
class Literal:
    """Represents a literal in a QBF formula."""
    
    variable: VariableIndex
    is_positive: bool
    occurrences: Set["Clause"] = field(default_factory=set)  # notably, occurence set is not frozen

    def literal_index(self) -> int:
        """Return the index of this literal in the formula."""
        return self.variable if self.is_positive else -self.variable
    
    def __eq__(self, value):
        """Return whether this literal is equal to another."""
        if not isinstance(value, Literal):
            return False
        return self.variable == value.variable and self.is_positive == value.is_positive
    
    def __hash__(self):
        """Return the hash of this literal."""
        return hash((self.variable, self.is_positive))


@dataclass(frozen=True)
class Variable:
    """Represents a variable in a QBF formula."""

    index: VariableIndex
    quantifier: QuantifierType
    positive: Literal = Literal(index, True)
    negative: Literal = Literal(index, False)

    def get_literal(self, is_positive: bool) -> Literal:
        """Return the positive or negative literal for this variable."""
        return self.positive if is_positive else self.negative

    def __hash__(self):
        """Return the hash of this variable."""
        return hash(self.index)
    
    def __eq__(self, value):
        """Return whether this variable is equal to another."""
        if not isinstance(value, Variable):
            return False
        return self.index == value.index


@dataclass(frozen=True)  # frozen to allow it to be used in a set
class Clause:
    """Represents a clause in a QBF formula."""

    literals: FrozenSet[Literal]
    index: ClauseIndex | None = None
    is_original: bool = False

    @staticmethod
    def from_literals(literals: Sequence[Literal], index: ClauseIndex, is_original: bool = False) -> "Clause":
        """Create a clause from a list of literals."""
        return Clause(frozenset(literals), index, is_original)

    def __hash__(self):
        """Return the hash of this clause."""
        return hash(frozenset(l.literal_index() for l in self.literals))

    def __eq__(self, other):
        """Return whether this clause is equal to another."""
        if not isinstance(other, Clause):
            return False
        return self.literals == other.literals

    def __repr__(self):
        """Return a string representation of this clause."""
        return f"Clause({self.literals})"
    
    def to_qdimacs(self) -> str:
        """Return a string representation of this clause in QDIMACS format."""
        clause_str = " ".join(str(l.literal_index()) for l in self.literals) + " 0"
        comment = f"c Clause {self.index}, {'original' if self.is_original else 'derived'}"
        return f"{comment}\n{clause_str}"
    
    @staticmethod
    def from_qdimacs(clause: Sequence[int], index: ClauseIndex, is_original: bool = False) -> "Clause":
        """Create a clause from a list of literals."""
        return Clause(frozenset(Literal(abs(lit), lit > 0) for lit in clause), index, is_original)



class Formula:
    """Working representation of a QBF formula.

    Represents a QBF formula as a graph of variables and clauses."""

    def __init__(self, qdimacs: parse.QDimacs):
        self.quantifiers: List[parse.QuantifierBlock] = qdimacs.quantifiers
        if len(qdimacs.quantifiers) != 0:
            raise NotImplementedError("Only quantifier-free formulas are supported.")
        self.variables: Dict[VariableIndex, Variable] = {}
        self.clauses: Dict[ClauseIndex, Clause] = {}

        # Largest variable index that MAY be in use.
        # 0 is not a valid variable index.
        self._largest_used_variable_index = 1
        # 
        self._largest_used_clause_index = 0

        for quantifier in qdimacs.quantifiers:
            for variable in quantifier.bound_variables:
                self.create_fresh_variable(quantifier.quantifier_type, variable)

        for index, clause in enumerate(qdimacs.clauses):
            clause = Clause.from_qdimacs(clause, index, is_original=True)
            self.add_clause(clause)
    
    def next_fresh_variable_index(self) -> int:
        """Return the next fresh variable index."""
        while self._largest_used_variable_index in self.variables:
            self._largest_used_variable_index += 1
        return self._largest_used_variable_index

    def create_fresh_variable(self, quantifier: QuantifierType, index: int | None = None) -> Variable:
        """Create a new variable with the given quantifier."""
        index = index or self.next_fresh_variable_index()
        self.variables[index] = Variable(index, quantifier)
        return self.variables[index]
    
    def next_fresh_clause_index(self) -> int:
        """Return the next fresh clause index."""
        while self._largest_used_clause_index in self.clauses:
            self._largest_used_clause_index += 1
        return self._largest_used_clause_index
    
    def is_clause_subsumed(self, clause: Clause) -> bool:
        """Return whether the given clause is subsumed by another clause."""
        if clause.index in self.clauses:
            return False
        # Optimization: find literal with smallest number of occurrences
        rarest_literal = min(clause.literals, key=lambda l: len(l.occurrences))
        # Optimization: only check clauses that are shorter
        candidate_clauses = (c for c in rarest_literal.occurrences if len(c.literals) <= len(clause.literals))
        return any(other.literals.issubset(clause.literals) for other in candidate_clauses)

    def add_clause(self, clause: Clause):
        """Add a clause to the formula."""
        assert clause.index not in self.clauses, f"Clause {clause.index} already in formula. {clause}, {self.clauses[clause.index]}"
        if not self.is_clause_subsumed(clause):
            self.clauses[clause.index] = clause

    def contains_empty_clause(self) -> bool:
        """Return whether this formula contains the empty clause."""
        empty_clause = Clause.from_literals((), index=0)  # can use any index here, as clause is discarded
        return empty_clause in self.clauses.values()

    def resolve(self, clause1: Clause, clause2: Clause, variable: Variable) -> Clause:
        """Resolve two clauses with respect to a variable."""
        both_literals = itertools.chain(clause1.literals, clause2.literals)
        literals = (l for l in both_literals if l.variable != variable.index)
        return Clause.from_literals(literals, self.next_fresh_clause_index(), is_original=False)
    
    def to_qdimacs(self) -> str:
        """Return a string representation of this formula in QDIMACS format."""
        clauses_in_order = sorted(self.clauses.values(), key=lambda c: c.index)
        clause_strings = (clause.to_qdimacs() for clause in clauses_in_order)
        header = f"p cnf {len(self.variables)} {len(self.clauses)}"
        all_lines = itertools.chain([header], clause_strings)
        return "\n".join(all_lines)

    # def eliminate_variable(self, variable: Variable) -> None:
    #     """Eliminate a variable from the formula."""
        
    #     # TODO!!!

    #     # Remove the variable from all clauses
    #     for clause in self.clauses.values():
    #         clause.literals -= {variable.positive, variable.negative}

    #     # Remove the variable from the set of variables
    #     del self.variables[variable.index]

